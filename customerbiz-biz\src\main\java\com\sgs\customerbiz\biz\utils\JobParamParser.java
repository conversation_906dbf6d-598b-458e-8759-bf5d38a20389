package com.sgs.customerbiz.biz.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * XXL-Job参数解析工具类
 * 
 * <AUTHOR> System
 */
@Slf4j
public class JobParamParser {
    
    private static final Pattern TIME_RANGE_PATTERN = Pattern.compile("(\\d+)([hdw])");
    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
    
    /**
     * 解析XXL-Job参数为InspectorioSyncParam对象
     * 
     * @param jobParam XXL-Job参数字符串
     * @param defaultRefSystemId 默认系统ID
     * @return InspectorioSyncParam对象
     */
    public static InspectorioSyncParam parseJobParam(String jobParam, Integer defaultRefSystemId) {
        InspectorioSyncParam param = new InspectorioSyncParam();
        
        if (Func.isNotBlank(jobParam)) {
            try {
                JSONObject jobParamObj = JSON.parseObject(jobParam);
                
                // 设置基本参数
                param.setSizeOfApi(jobParamObj.getInteger("sizeOfApi"));
                param.setSizeOfDb(jobParamObj.getInteger("sizeOfDb"));
                param.setMaxSizeOfApi(jobParamObj.getInteger("maxSizeOfApi"));
                param.setTimeRange(jobParamObj.getString("timeRange"));
                param.setUpdatedFrom(jobParamObj.getString("updatedFrom"));
                param.setUpdatedTo(jobParamObj.getString("updatedTo"));
                param.setRefSystemId(jobParamObj.getInteger("refSystemId"));
                
                // 设置默认值
                if (param.getSizeOfApi() == null) {
                    param.setSizeOfApi(50);
                }
                if (param.getSizeOfDb() == null) {
                    param.setSizeOfDb(100);
                }
                if (param.getMaxSizeOfApi() == null) {
                    param.setMaxSizeOfApi(1000);
                }
                if (param.getRefSystemId() == null) {
                    param.setRefSystemId(defaultRefSystemId);
                }
                
                // 解析时间范围
                parseTimeRange(param);
                
            } catch (Exception e) {
                log.warn("解析Job参数失败，使用默认参数: {}", e.getMessage());
                setDefaultParams(param, defaultRefSystemId);
            }
        } else {
            setDefaultParams(param, defaultRefSystemId);
        }
        
        return param;
    }
    
    /**
     * 设置默认参数
     */
    private static void setDefaultParams(InspectorioSyncParam param, Integer defaultRefSystemId) {
        param.setSizeOfApi(50);
        param.setSizeOfDb(100);
        param.setMaxSizeOfApi(1000);
        param.setTimeRange("1h");
        param.setRefSystemId(defaultRefSystemId);
        parseTimeRange(param);
    }
    
    /**
     * 解析时间范围参数
     * 支持相对时间（如1h, 2d, 1w）和绝对时间
     */
    public static void parseTimeRange(InspectorioSyncParam param) {
        String timeRange = param.getTimeRange();
        
        if (Func.isBlank(timeRange)) {
            // 默认1小时
            timeRange = "1h";
            param.setTimeRange(timeRange);
        }
        
        // 如果已经设置了绝对时间，则不需要解析
        if (Func.isNotBlank(param.getUpdatedFrom()) && Func.isNotBlank(param.getUpdatedTo())) {
            return;
        }
        
        // 解析相对时间
        Matcher matcher = TIME_RANGE_PATTERN.matcher(timeRange);
        if (matcher.matches()) {
            int amount = Integer.parseInt(matcher.group(1));
            String unit = matcher.group(2);
            
            LocalDateTime endTime = LocalDateTime.now(ZoneOffset.UTC);
            LocalDateTime startTime;
            
            switch (unit) {
                case "h":
                    startTime = endTime.minusHours(amount);
                    break;
                case "d":
                    startTime = endTime.minusDays(amount);
                    break;
                case "w":
                    startTime = endTime.minusWeeks(amount);
                    break;
                default:
                    startTime = endTime.minusHours(1);
                    break;
            }
            
            param.setUpdatedFrom(startTime.format(ISO_FORMATTER));
            param.setUpdatedTo(endTime.format(ISO_FORMATTER));
            
        } else {
            // 尝试解析绝对时间格式 "2023-07-01,2023-07-07"
            if (timeRange.contains(",")) {
                String[] times = timeRange.split(",");
                if (times.length == 2) {
                    try {
                        LocalDateTime startTime = LocalDateTime.parse(times[0] + "T00:00:00");
                        LocalDateTime endTime = LocalDateTime.parse(times[1] + "T23:59:59");
                        
                        param.setUpdatedFrom(startTime.atOffset(ZoneOffset.UTC).format(ISO_FORMATTER));
                        param.setUpdatedTo(endTime.atOffset(ZoneOffset.UTC).format(ISO_FORMATTER));
                    } catch (Exception e) {
                        log.warn("解析绝对时间失败: {}, 使用默认1小时", timeRange);
                        parseTimeRange(param);
                    }
                }
            } else {
                log.warn("无法识别的时间范围格式: {}, 使用默认1小时", timeRange);
                param.setTimeRange("1h");
                parseTimeRange(param);
            }
        }
    }
    
    /**
     * 验证参数有效性
     */
    public static boolean validateParam(InspectorioSyncParam param) {
        if (param == null) {
            return false;
        }
        
        if (param.getRefSystemId() == null) {
            log.error("系统ID不能为空");
            return false;
        }
        
        if (Func.isBlank(param.getUpdatedFrom()) || Func.isBlank(param.getUpdatedTo())) {
            log.error("时间范围参数无效");
            return false;
        }
        
        if (param.getSizeOfApi() == null || param.getSizeOfApi() <= 0) {
            log.error("API查询数量必须大于0");
            return false;
        }
        
        if (param.getSizeOfDb() == null || param.getSizeOfDb() <= 0) {
            log.error("数据库处理数量必须大于0");
            return false;
        }
        
        return true;
    }
}
