package com.sgs.customerbiz.model.trf.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手动同步TRF信息响应对象
 * 
 * <AUTHOR> System
 */
@Data
@ApiModel(description = "手动同步TRF信息响应")
public class ManualSyncResponse {
    
    /**
     * 同步的总数量
     */
    @ApiModelProperty(value = "同步的总数量", example = "150")
    private Integer syncCount;
    
    /**
     * 更新的数量
     */
    @ApiModelProperty(value = "更新的数量", example = "30")
    private Integer updateCount;
    
    /**
     * 插入的数量
     */
    @ApiModelProperty(value = "插入的数量", example = "120")
    private Integer insertCount;
    
    /**
     * 同步开始时间
     */
    @ApiModelProperty(value = "同步开始时间", example = "2023-07-01T10:00:00Z")
    private String startTime;
    
    /**
     * 同步结束时间
     */
    @ApiModelProperty(value = "同步结束时间", example = "2023-07-01T10:05:00Z")
    private String endTime;
    
    /**
     * 同步耗时（毫秒）
     */
    @ApiModelProperty(value = "同步耗时（毫秒）", example = "5000")
    private Long duration;
    
    /**
     * 系统ID
     */
    @ApiModelProperty(value = "系统ID", example = "10030")
    private Integer refSystemId;
    
    /**
     * 构造函数
     */
    public ManualSyncResponse() {
    }
    
    /**
     * 构造函数
     * @param syncCount 同步总数
     * @param updateCount 更新数量
     * @param insertCount 插入数量
     */
    public ManualSyncResponse(Integer syncCount, Integer updateCount, Integer insertCount) {
        this.syncCount = syncCount;
        this.updateCount = updateCount;
        this.insertCount = insertCount;
    }
}
