package com.sgs.customerbiz.biz.convert;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * InspectorioTrfConverter单元测试
 * 
 * <AUTHOR> System
 */
class InspectorioTrfConverterTest {

    private InspectorioTrfConverter converter;

    @BeforeEach
    void setUp() {
        converter = new InspectorioTrfConverter();
    }

    @Test
    void testConvertToTrfInfoPO_ValidData() {
        // 准备测试数据
        JSONObject jsonData = new JSONObject();
        jsonData.put("uuid", "test-uuid-123");
        jsonData.put("labTestId", "TRF-001");
        jsonData.put("status", "ACTIVE");
        jsonData.put("createdAt", "2023-07-01T10:00:00Z");
        jsonData.put("updatedAt", "2023-07-01T12:00:00Z");
        
        Integer refSystemId = 10030;

        // 执行测试
        InspectorioTrfInfoPO result = converter.convertToTrfInfoPO(jsonData, refSystemId);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-uuid-123", result.getUuid());
        assertEquals("TRF-001", result.getTrfNo());
        assertEquals("ACTIVE", result.getTrfStatus());
        assertEquals(refSystemId, result.getRefSystemId());
        assertNotNull(result.getCreatedAt());
        assertNotNull(result.getUpdatedAt());
        assertNotNull(result.getRawData());
        assertEquals(1, result.getActiveIndicator());
    }

    @Test
    void testConvertToTrfInfoPO_NullData() {
        // 执行测试
        InspectorioTrfInfoPO result = converter.convertToTrfInfoPO(null, 10030);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testConvertToTrfInfoPO_MissingRequiredFields() {
        // 准备测试数据 - 缺少必填字段
        JSONObject jsonData = new JSONObject();
        jsonData.put("status", "ACTIVE");
        
        Integer refSystemId = 10030;

        // 执行测试
        InspectorioTrfInfoPO result = converter.convertToTrfInfoPO(jsonData, refSystemId);

        // 验证结果
        assertNull(result); // 应该返回null因为缺少必填字段
    }

    @Test
    void testConvertToTrfInfoPOList_ValidDataList() {
        // 准备测试数据
        JSONObject jsonData1 = new JSONObject();
        jsonData1.put("uuid", "test-uuid-1");
        jsonData1.put("labTestId", "TRF-001");
        jsonData1.put("status", "ACTIVE");
        jsonData1.put("createdAt", "2023-07-01T10:00:00Z");

        JSONObject jsonData2 = new JSONObject();
        jsonData2.put("uuid", "test-uuid-2");
        jsonData2.put("labTestId", "TRF-002");
        jsonData2.put("status", "PENDING");
        jsonData2.put("createdAt", "2023-07-01T11:00:00Z");

        List<JSONObject> jsonDataList = Arrays.asList(jsonData1, jsonData2);
        Integer refSystemId = 10030;

        // 执行测试
        List<InspectorioTrfInfoPO> result = converter.convertToTrfInfoPOList(jsonDataList, refSystemId);

        // 验证结果
        assertNotNull(result);
        // 注意：由于缺少必填字段，可能会过滤掉一些记录
        assertTrue(result.size() <= 2);
        if (!result.isEmpty()) {
            assertNotNull(result.get(0).getUuid());
        }
    }

    @Test
    void testConvertToTrfInfoPOList_EmptyList() {
        // 执行测试
        List<InspectorioTrfInfoPO> result = converter.convertToTrfInfoPOList(null, 10030);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testUpdateTrfInfoPO_ValidData() {
        // 准备测试数据
        InspectorioTrfInfoPO existingPO = new InspectorioTrfInfoPO();
        existingPO.setUuid("test-uuid");
        existingPO.setTrfNo("TRF-001");
        existingPO.setTrfStatus("PENDING");
        
        JSONObject newData = new JSONObject();
        newData.put("uuid", "test-uuid");
        newData.put("labTestId", "TRF-001");
        newData.put("status", "ACTIVE");
        newData.put("updatedAt", "2023-07-01T15:00:00Z");

        // 执行测试
        InspectorioTrfInfoPO result = converter.updateTrfInfoPO(existingPO, newData);

        // 验证结果
        assertNotNull(result);
        assertEquals("ACTIVE", result.getTrfStatus());
        assertNotNull(result.getUpdatedAt());
        assertNotNull(result.getModifiedDate());
    }

    @Test
    void testUpdateTrfInfoPO_NullData() {
        // 准备测试数据
        InspectorioTrfInfoPO existingPO = new InspectorioTrfInfoPO();
        existingPO.setTrfStatus("PENDING");

        // 执行测试
        InspectorioTrfInfoPO result = converter.updateTrfInfoPO(existingPO, null);

        // 验证结果
        assertEquals(existingPO, result);
        assertEquals("PENDING", result.getTrfStatus()); // 状态应该保持不变
    }

    @Test
    void testExtractKeyInfo_ValidData() {
        // 准备测试数据
        JSONObject jsonData = new JSONObject();
        jsonData.put("uuid", "test-uuid-123");
        jsonData.put("labTestId", "TRF-001");
        jsonData.put("status", "ACTIVE");

        // 执行测试
        String result = converter.extractKeyInfo(jsonData);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("test-uuid-123"));
        assertTrue(result.contains("TRF-001"));
        assertTrue(result.contains("ACTIVE"));
    }

    @Test
    void testExtractKeyInfo_NullData() {
        // 执行测试
        String result = converter.extractKeyInfo(null);

        // 验证结果
        assertEquals("null", result);
    }
}
