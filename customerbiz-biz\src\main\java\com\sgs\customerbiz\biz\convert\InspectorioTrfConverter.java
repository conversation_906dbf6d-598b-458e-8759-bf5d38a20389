package com.sgs.customerbiz.biz.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Inspectorio TRF数据转换器
 * 负责将Inspectorio API响应数据转换为数据库实体对象
 * 
 * <AUTHOR> System
 */
@Component
@Slf4j
public class InspectorioTrfConverter {
    
    /**
     * 支持的日期格式列表
     */
    private static final List<String> DATE_PATTERNS = List.of(
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.S'Z'",
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd"
    );
    
    /**
     * 将JSON对象转换为InspectorioTrfInfoPO
     * 
     * @param jsonData JSON数据
     * @param refSystemId 系统ID
     * @return InspectorioTrfInfoPO对象
     */
    public InspectorioTrfInfoPO convertToTrfInfoPO(JSONObject jsonData, Integer refSystemId) {
        if (jsonData == null) {
            return null;
        }
        
        InspectorioTrfInfoPO po = new InspectorioTrfInfoPO();
        
        try {
            // 设置基本字段
            po.setUuid(jsonData.getString("uuid"));
            po.setTrfNo(jsonData.getString("labTestId"));
            po.setRefSystemId(refSystemId);
            po.setTrfStatus(jsonData.getString("status"));
            
            // 解析日期字段
            po.setCreatedAt(parseDate(jsonData.getString("createdAt")));
            po.setUpdatedAt(parseDate(jsonData.getString("updatedAt")));
            
            // 设置原始JSON数据
            po.setRawData(jsonData.toJSONString());
            
            // 设置默认值
            po.setActiveIndicator(ActiveIndicatorEnum.ACTIVE.getValue());
            po.setCreatedDate(new Date());
            po.setModifiedDate(new Date());
            
            // 验证必填字段
            if (!validateRequiredFields(po)) {
                log.warn("TRF数据验证失败，缺少必填字段: {}", jsonData.toJSONString());
                return null;
            }
            
        } catch (Exception e) {
            log.error("转换TRF数据失败: {}", e.getMessage(), e);
            return null;
        }
        
        return po;
    }
    
    /**
     * 批量转换JSON数据列表
     * 
     * @param jsonDataList JSON数据列表
     * @param refSystemId 系统ID
     * @return InspectorioTrfInfoPO列表
     */
    public List<InspectorioTrfInfoPO> convertToTrfInfoPOList(List<JSONObject> jsonDataList, Integer refSystemId) {
        if (jsonDataList == null || jsonDataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<InspectorioTrfInfoPO> result = new ArrayList<>();
        
        for (JSONObject jsonData : jsonDataList) {
            InspectorioTrfInfoPO po = convertToTrfInfoPO(jsonData, refSystemId);
            if (po != null) {
                result.add(po);
            }
        }
        
        return result;
    }
    
    /**
     * 解析日期字符串
     * 
     * @param dateStr 日期字符串
     * @return Date对象，解析失败返回null
     */
    private Date parseDate(String dateStr) {
        if (Func.isBlank(dateStr)) {
            return null;
        }
        
        for (String pattern : DATE_PATTERNS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        log.warn("无法解析日期格式: {}", dateStr);
        return null;
    }
    
    /**
     * 验证必填字段
     * 
     * @param po InspectorioTrfInfoPO对象
     * @return 验证结果
     */
    private boolean validateRequiredFields(InspectorioTrfInfoPO po) {
        if (po == null) {
            return false;
        }
        
        if (Func.isBlank(po.getUuid())) {
            log.error("UUID不能为空");
            return false;
        }
        
        if (Func.isBlank(po.getTrfNo())) {
            log.error("TRF编号不能为空");
            return false;
        }
        
        if (po.getRefSystemId() == null) {
            log.error("系统ID不能为空");
            return false;
        }
        
        if (Func.isBlank((String) po.getRawData())) {
            log.error("原始数据不能为空");
            return false;
        }
        
        return true;
    }
    
    /**
     * 更新现有PO对象的数据
     * 
     * @param existingPO 现有的PO对象
     * @param jsonData 新的JSON数据
     * @return 更新后的PO对象
     */
    public InspectorioTrfInfoPO updateTrfInfoPO(InspectorioTrfInfoPO existingPO, JSONObject jsonData) {
        if (existingPO == null || jsonData == null) {
            return existingPO;
        }
        
        try {
            // 更新可变字段
            existingPO.setTrfStatus(jsonData.getString("status"));
            existingPO.setUpdatedAt(parseDate(jsonData.getString("updatedAt")));
            existingPO.setRawData(jsonData.toJSONString());
            existingPO.setModifiedDate(new Date());
            
        } catch (Exception e) {
            log.error("更新TRF数据失败: {}", e.getMessage(), e);
        }
        
        return existingPO;
    }
    
    /**
     * 从JSON数据中提取关键信息用于日志记录
     * 
     * @param jsonData JSON数据
     * @return 关键信息字符串
     */
    public String extractKeyInfo(JSONObject jsonData) {
        if (jsonData == null) {
            return "null";
        }
        
        return String.format("uuid=%s, labTestId=%s, status=%s", 
            jsonData.getString("uuid"),
            jsonData.getString("labTestId"),
            jsonData.getString("status"));
    }
}
