package com.sgs.customerbiz.model.trf.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 手动同步TRF信息请求对象
 * 
 * <AUTHOR> System
 */
@Data
@ApiModel(description = "手动同步TRF信息请求")
public class ManualSyncRequest {
    
    /**
     * 系统ID (10030=Lululemon, 10028=Target)
     */
    @NotNull(message = "系统ID不能为空")
    @ApiModelProperty(value = "系统ID (10030=Lululemon, 10028=Target)", required = true, example = "10030")
    private Integer refSystemId;
    
    /**
     * 时间范围，支持相对时间和绝对时间
     * 相对时间格式: "1h", "2d", "1w" (小时、天、周)
     * 绝对时间格式: "2023-07-01,2023-07-07" (开始时间,结束时间)
     */
    @NotNull(message = "时间范围不能为空")
    @ApiModelProperty(value = "时间范围", required = true, example = "1h")
    private String timeRange;
    
    /**
     * API每次查询的数量，可选参数
     */
    @ApiModelProperty(value = "API每次查询的数量", example = "50")
    private Integer sizeOfApi;
    
    /**
     * 数据库每次处理的数量，可选参数
     */
    @ApiModelProperty(value = "数据库每次处理的数量", example = "100")
    private Integer sizeOfDb;
    
    /**
     * API查询的最大数量限制，可选参数
     */
    @ApiModelProperty(value = "API查询的最大数量限制", example = "1000")
    private Integer maxSizeOfApi;
}
