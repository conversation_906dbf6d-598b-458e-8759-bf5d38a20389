# TRF分页同步功能实现说明

## 概述

根据您的要求，我已经重构了 `syncTrfInfo` 方法，参考现有的 `loadData` 和 `loadPageData` 方法实现了分页处理逻辑。

## 主要改进

### 1. 分页处理架构

```java
syncTrfInfo() 
├── loadTrfData() - 加载所有分页数据
│   ├── loadTrfPageData() - 加载单页数据
│   │   ├── buildTrfQueryRequest() - 构建分页请求
│   │   └── parseTrfPageResponse() - 解析分页响应
│   └── 循环处理所有页面
└── 批量处理数据库操作
```

### 2. API调用参数

现在 `queryCustomerTrfList` 的调用参数完全符合您的要求：

```json
{
    "refSystemId": "<Lululemon>|<Target>",  
    "updatedFrom": "$.trfStartDate",
    "updatedTo": "$.trfEndDate",
    "offset": "$.offset",
    "limit": "$.rows"
}
```

### 3. 响应数据解析

按照您提供的格式解析返回值：

```java
Object total = JSONPath.eval(o.getData(), "$.total");
Object limit = JSONPath.eval(o.getData(), "$.limit");
Object offset = JSONPath.eval(o.getData(), "$.offset");
Object dataList = JSONPath.eval(o.getData(), "$.list");
```

## 核心实现

### 1. 分页数据加载

```java
private List<TrfPageData> loadTrfData(InspectorioSyncParam param) {
    final int limit = param.getSizeOfApi();
    final int maxSize = param.getMaxSizeOfApi();
    List<TrfPageData> allPageData = new ArrayList<>();
    
    // 加载第一页数据以获取总数
    TrfPageData firstPage = loadTrfPageData(param, 0, limit);
    allPageData.add(firstPage);
    
    // 计算总页数并加载剩余页面
    int totalRecords = Math.min(firstPage.getTotal(), maxSize);
    int totalPages = (int) Math.ceil((double) totalRecords / limit);
    
    for (int page = 1; page < totalPages; page++) {
        int offset = page * limit;
        if (offset >= maxSize) break;
        TrfPageData pageData = loadTrfPageData(param, offset, limit);
        allPageData.add(pageData);
    }
    
    return allPageData;
}
```

### 2. 单页数据处理

```java
private TrfPageData loadTrfPageData(InspectorioSyncParam param, int offset, int limit) {
    // 构建查询请求
    JSONObject requestBody = buildTrfQueryRequest(param, offset, limit);
    
    // 调用API
    CustomResult<Object> apiResult = iLayerClient.queryCustomerTrfList(requestBody);
    
    // 解析分页响应
    return parseTrfPageResponse(apiResult.getData());
}
```

### 3. 数据结构

新增了两个内部类来管理分页数据：

```java
@Data
public static class TrfPageData {
    private final int total;
    private final int limit;
    private final int offset;
    private final List<JSONObject> dataList;
}

@Data
public static class TrfDatabaseResult {
    private final int insertCount;
    private final int updateCount;
}
```

## 处理流程

1. **初始化**: 从第一页获取总记录数
2. **分页计算**: 根据 `sizeOfApi` 和 `maxSizeOfApi` 计算需要加载的页数
3. **并发加载**: 逐页加载数据（可以后续优化为并发加载）
4. **数据转换**: 将每页的JSON数据转换为数据库实体
5. **批量操作**: 执行数据库的插入和更新操作
6. **统计报告**: 记录处理的总数、插入数、更新数

## 配置参数

- `sizeOfApi`: 每页API请求的记录数（对应 `limit`）
- `maxSizeOfApi`: 最大处理记录数限制
- `sizeOfDb`: 数据库批量操作的批次大小

## 日志输出

```
TRF数据分页加载，总记录数: 1500, 每页: 50, 总页数: 30, 最大限制: 1000
处理第 1 页数据，包含 50 条记录
处理第 2 页数据，包含 50 条记录
...
TRF信息同步完成，总计处理: 1000 条，插入: 800 条，更新: 200 条
```

## 性能优化建议

1. **并发处理**: 可以将页面加载改为并发执行
2. **内存管理**: 对于大量数据，可以考虑流式处理
3. **错误恢复**: 单页失败不影响其他页面的处理
4. **进度监控**: 添加进度回调机制

## 兼容性

- 完全兼容现有的 `InspectorioSyncParam` 参数结构
- 保持与现有调度器和API接口的兼容性
- 遵循现有的错误处理和日志记录模式

这个实现确保了与现有系统的完美集成，同时提供了高效的分页处理能力。
