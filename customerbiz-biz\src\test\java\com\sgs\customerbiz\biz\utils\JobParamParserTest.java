package com.sgs.customerbiz.biz.utils;

import com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JobParamParser单元测试
 * 
 * <AUTHOR> System
 */
class JobParamParserTest {

    @Test
    void testParseJobParam_WithValidJson() {
        // 准备测试数据
        String jobParam = "{\"sizeOfApi\":50,\"sizeOfDb\":100,\"timeRange\":\"1h\",\"refSystemId\":10030}";
        Integer defaultRefSystemId = 10028;

        // 执行测试
        InspectorioSyncParam result = JobParamParser.parseJobParam(jobParam, defaultRefSystemId);

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getSizeOfApi());
        assertEquals(100, result.getSizeOfDb());
        assertEquals("1h", result.getTimeRange());
        assertEquals(10030, result.getRefSystemId());
        assertNotNull(result.getUpdatedFrom());
        assertNotNull(result.getUpdatedTo());
    }

    @Test
    void testParseJobParam_WithEmptyParam() {
        // 准备测试数据
        String jobParam = "";
        Integer defaultRefSystemId = 10030;

        // 执行测试
        InspectorioSyncParam result = JobParamParser.parseJobParam(jobParam, defaultRefSystemId);

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getSizeOfApi());
        assertEquals(100, result.getSizeOfDb());
        assertEquals("1h", result.getTimeRange());
        assertEquals(10030, result.getRefSystemId());
        assertNotNull(result.getUpdatedFrom());
        assertNotNull(result.getUpdatedTo());
    }

    @Test
    void testParseJobParam_WithNullParam() {
        // 准备测试数据
        String jobParam = null;
        Integer defaultRefSystemId = 10028;

        // 执行测试
        InspectorioSyncParam result = JobParamParser.parseJobParam(jobParam, defaultRefSystemId);

        // 验证结果
        assertNotNull(result);
        assertEquals(50, result.getSizeOfApi());
        assertEquals(100, result.getSizeOfDb());
        assertEquals("1h", result.getTimeRange());
        assertEquals(10028, result.getRefSystemId());
    }

    @Test
    void testParseTimeRange_RelativeTime() {
        // 准备测试数据
        InspectorioSyncParam param = new InspectorioSyncParam();
        param.setTimeRange("2h");

        // 执行测试
        JobParamParser.parseTimeRange(param);

        // 验证结果
        assertNotNull(param.getUpdatedFrom());
        assertNotNull(param.getUpdatedTo());
        assertTrue(param.getUpdatedFrom().endsWith("Z"));
        assertTrue(param.getUpdatedTo().endsWith("Z"));
    }

    @Test
    void testParseTimeRange_AbsoluteTime() {
        // 准备测试数据
        InspectorioSyncParam param = new InspectorioSyncParam();
        param.setTimeRange("2023-07-01,2023-07-02");

        // 执行测试
        JobParamParser.parseTimeRange(param);

        // 验证结果
        assertNotNull(param.getUpdatedFrom());
        assertNotNull(param.getUpdatedTo());
        assertTrue(param.getUpdatedFrom().contains("2023-07-01"));
        assertTrue(param.getUpdatedTo().contains("2023-07-02"));
    }

    @Test
    void testValidateParam_ValidParam() {
        // 准备测试数据
        InspectorioSyncParam param = new InspectorioSyncParam();
        param.setRefSystemId(10030);
        param.setSizeOfApi(50);
        param.setSizeOfDb(100);
        param.setUpdatedFrom("2023-07-01T00:00:00Z");
        param.setUpdatedTo("2023-07-01T23:59:59Z");

        // 执行测试
        boolean result = JobParamParser.validateParam(param);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testValidateParam_NullParam() {
        // 执行测试
        boolean result = JobParamParser.validateParam(null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testValidateParam_MissingRefSystemId() {
        // 准备测试数据
        InspectorioSyncParam param = new InspectorioSyncParam();
        param.setSizeOfApi(50);
        param.setSizeOfDb(100);
        param.setUpdatedFrom("2023-07-01T00:00:00Z");
        param.setUpdatedTo("2023-07-01T23:59:59Z");

        // 执行测试
        boolean result = JobParamParser.validateParam(param);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void testValidateParam_InvalidSizeOfApi() {
        // 准备测试数据
        InspectorioSyncParam param = new InspectorioSyncParam();
        param.setRefSystemId(10030);
        param.setSizeOfApi(0);
        param.setSizeOfDb(100);
        param.setUpdatedFrom("2023-07-01T00:00:00Z");
        param.setUpdatedTo("2023-07-01T23:59:59Z");

        // 执行测试
        boolean result = JobParamParser.validateParam(param);

        // 验证结果
        assertFalse(result);
    }
}
