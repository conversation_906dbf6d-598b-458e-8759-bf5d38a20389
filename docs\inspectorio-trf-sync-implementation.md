# Inspectorio TRF同步功能实现文档

## 概述

本文档描述了SCI-1842 Inspectorio TRF TODO List功能的实现细节。该功能实现了从Inspectorio系统同步TRF（Testing Request Form）信息到本地数据库的完整解决方案。

## 实现的功能

### 1. 数据模型和参数类
- **InspectorioSyncParam**: 扩展的同步参数类，支持TRF同步的时间范围和系统ID配置
- **ManualSyncRequest/Response**: 手动同步API的请求和响应对象
- **InspectorioTrfInfoPO**: 已存在的TRF信息数据库实体（复用现有）

### 2. 业务逻辑层
- **InspectorioDataBizService**: 扩展现有服务，添加了TRF同步方法
  - `syncLululemonTrfInfo()`: 同步Lululemon TRF信息
  - `syncTargetTrfInfo()`: 同步Target TRF信息
  - `syncTrfInfo()`: 核心同步逻辑实现

### 3. 数据转换器
- **InspectorioTrfConverter**: 负责JSON数据到数据库实体的转换
  - 支持多种日期格式解析
  - 数据验证和清洗
  - 批量转换支持

### 4. 定时任务调度器
- **SyncLululemonTrfInfoXXLJobScheduler**: Lululemon TRF同步调度器
- **SyncTargetTrfInfoXXLJobScheduler**: Target TRF同步调度器
- 支持XXL-Job参数配置和错误处理

### 5. 工具类
- **JobParamParser**: XXL-Job参数解析工具
  - 支持相对时间（1h, 2d, 1w）和绝对时间解析
  - 参数验证和默认值设置

### 6. Web接口
- **TodoListController**: 扩展现有控制器，添加手动同步接口
  - `POST /todoList/manualSync`: 手动触发TRF同步

## 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Web层                                     │
│  TodoListController.manualSync()                           │
├─────────────────────────────────────────────────────────────┤
│                    调度层                                    │
│  SyncLululemonTrfInfoXXLJobScheduler                       │
│  SyncTargetTrfInfoXXLJobScheduler                          │
├─────────────────────────────────────────────────────────────┤
│                    业务层                                    │
│  InspectorioDataBizService                                 │
│  - syncLululemonTrfInfo()                                  │
│  - syncTargetTrfInfo()                                     │
├─────────────────────────────────────────────────────────────┤
│                    转换层                                    │
│  InspectorioTrfConverter                                   │
├─────────────────────────────────────────────────────────────┤
│                    数据层                                    │
│  InspectorioTrfInfoMapper                                  │
│  tb_inspectorio_trf_info表                                 │
├─────────────────────────────────────────────────────────────┤
│                    集成层                                    │
│  LocalILayerClient.queryCustomerTrfList()                 │
└─────────────────────────────────────────────────────────────┘
```

## 配置说明

### XXL-Job任务配置

#### Lululemon TRF同步任务
- **任务名称**: syncLululemonTrfInfoHandler
- **参数示例**:
```json
{
  "sizeOfApi": 50,
  "sizeOfDb": 100,
  "maxSizeOfApi": 1000,
  "timeRange": "1h"
}
```

#### Target TRF同步任务
- **任务名称**: syncTargetTrfInfoHandler
- **参数示例**:
```json
{
  "sizeOfApi": 50,
  "sizeOfDb": 100,
  "maxSizeOfApi": 1000,
  "timeRange": "1h"
}
```

### 时间范围参数格式
- **相对时间**: "1h", "2d", "1w" (小时、天、周)
- **绝对时间**: "2023-07-01,2023-07-07" (开始时间,结束时间)

## API接口

### 手动同步接口

**URL**: `POST /todoList/manualSync`

**请求参数**:
```json
{
  "refSystemId": 10030,
  "timeRange": "1h",
  "sizeOfApi": 50,
  "sizeOfDb": 100,
  "maxSizeOfApi": 1000
}
```

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "syncCount": 0,
    "updateCount": 0,
    "insertCount": 0,
    "startTime": "2023-07-01T10:00:00Z",
    "endTime": "2023-07-01T10:05:00Z",
    "duration": 5000,
    "refSystemId": 10030
  }
}
```

## 系统ID映射

- **Lululemon**: 10030 (RefSystemIdEnum.LULULEMON_INSPECTORIO)
- **Target**: 10028 (RefSystemIdEnum.TARGET_INSPECTORIO)

## 数据库表结构

使用现有的`tb_inspectorio_trf_info`表，包含以下关键字段：
- `uuid`: Customer UUID
- `trf_no`: Customer TRF No (labTestId)
- `ref_system_id`: 系统ID
- `created_at`: Customer创建时间
- `updated_at`: Customer更新时间
- `trf_status`: Customer状态
- `raw_data`: 原始JSON数据

## 测试

实现了以下单元测试：
- `JobParamParserTest`: 参数解析工具测试
- `InspectorioTrfConverterTest`: 数据转换器测试
- `TodoListControllerTest`: 控制器接口测试

## 部署步骤

1. **数据库**: 确保`tb_inspectorio_trf_info`表已存在
2. **XXL-Job**: 配置两个定时任务
3. **应用部署**: 部署包含新功能的应用版本
4. **配置验证**: 验证系统ID和API配置正确

## 监控和日志

- 所有同步操作都有详细的日志记录
- 支持XXL-Job的任务执行监控
- 异常情况会记录错误日志并发送邮件通知（如果配置）

## 注意事项

1. 确保Inspectorio API的访问权限和网络连通性
2. 监控同步任务的执行频率，避免对外部系统造成压力
3. 定期检查数据同步的准确性和完整性
4. 注意处理大量数据时的内存使用情况
