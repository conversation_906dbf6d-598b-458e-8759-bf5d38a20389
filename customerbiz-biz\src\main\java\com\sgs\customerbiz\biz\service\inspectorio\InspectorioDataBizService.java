package com.sgs.customerbiz.biz.service.inspectorio;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.InspectorioTrfConverter;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.sgs.customerbiz.biz.utils.JobParamParser;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.InspectorioTrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioPackageInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTestLineInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoExample;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.EmailClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.dto.EMailRequest;
import com.sgs.customerbiz.integration.dto.inspectorio.ResultData;
import com.sgs.customerbiz.integration.dto.inspectorio.RevisableData;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.customerbiz.model.tuple.Pair;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.Arrays;

@Service
@Slf4j
public class InspectorioDataBizService {

    private static final String CONFIG_CUSTOMER_CONFIG = "customerConfig";
    private static final String OPERATION_INSERT = "insert";
    private static final String OPERATION_UPDATE = "update";
    private static final String OPERATION_DELETE = "delete";

    @Data
    public static class InspectorioSyncParam {
        private Integer sizeOfApi;
        private Integer sizeOfDb;
        private Integer maxSizeOfApi = Integer.MAX_VALUE;
        private List<String> datePatterns = Arrays.asList(
            "yyyy-MM-dd'T'HH:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.SS'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.S'Z'"
        );
    }

    /**
     * 组合同步操作结果
     */
    @Data
    public static class CombinedSyncOperations {
        private GenericDatabaseOperations<InspectorioPackageInfoPO> packageOperations;
        private GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOperations;
        
        public CombinedSyncOperations(GenericDatabaseOperations<InspectorioPackageInfoPO> packageOperations, GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOperations) {
            this.packageOperations = packageOperations;
            this.testLineOperations = testLineOperations;
        }
        
        public boolean hasAnyOperations() {
            return hasPackageOperations() || hasTestLineOperations();
        }
        
        public boolean hasPackageOperations() {
            return packageOperations != null && (!packageOperations.toInsert.isEmpty() || !packageOperations.toUpdate.isEmpty() || !packageOperations.toDelete.isEmpty());
        }
        
        public boolean hasTestLineOperations() {
            return testLineOperations != null && (!testLineOperations.toInsert.isEmpty() || !testLineOperations.toUpdate.isEmpty() || !testLineOperations.toDelete.isEmpty());
        }
    }

    private final LocalILayerClient iLayerClient;
    private final PackageMapperMgr packageMapperMgr;
    private final TestLineMapperMgr testLineMapperMgr;
    private final EmailClient emailClient;
    private final ConfigClient configClient;
    private final InspectorioTrfInfoMapper inspectorioTrfInfoMapper;
    private final InspectorioTrfConverter inspectorioTrfConverter;

    public InspectorioDataBizService(LocalILayerClient iLayerClient,
                                     PackageMapperMgr packageMapperMgr,
                                     TestLineMapperMgr testLineMapperMgr,
                                     EmailClient emailClient,
                                     ConfigClient configClient,
                                     InspectorioTrfInfoMapper inspectorioTrfInfoMapper,
                                     InspectorioTrfConverter inspectorioTrfConverter) {
        this.iLayerClient = iLayerClient;
        this.packageMapperMgr = packageMapperMgr;
        this.testLineMapperMgr = testLineMapperMgr;
        this.emailClient = emailClient;
        this.configClient = configClient;
        this.inspectorioTrfInfoMapper = inspectorioTrfInfoMapper;
        this.inspectorioTrfConverter = inspectorioTrfConverter;
    }

    public void syncLululemonPackageInfo(InspectorioSyncParam param) {
        syncInspectorioData("GetTestPackageList", packageMapperMgr, "PackageInfo", param);
    }

    public void syncLululemonTestLineInfo(InspectorioSyncParam param) {
        syncInspectorioData("GetTestPropertyList", testLineMapperMgr, "TestLineInfo", param);
    }

    /**
     * 同时同步Package和TestLine信息
     */
    public void syncLululemonPackageAndTestLineInfo(InspectorioSyncParam param) {
        CombinedSyncOperations combinedOperations = syncInspectorioDataCombined(param);
        
        // 如果有任何操作，发送邮件通知
        if (combinedOperations.hasAnyOperations()) {
            sendCombinedSyncNotificationEmail(combinedOperations);
        }
        
        JobLogUtil.info(log, "syncLululemonPackageAndTestLineInfo完成，Package操作: 新增={}, 更新={}, 删除={}, TestLine操作: 新增={}, 更新={}, 删除={}", 
            combinedOperations.hasPackageOperations() ? combinedOperations.getPackageOperations().toInsert.size() : 0,
            combinedOperations.hasPackageOperations() ? combinedOperations.getPackageOperations().toUpdate.size() : 0,
            combinedOperations.hasPackageOperations() ? combinedOperations.getPackageOperations().toDelete.size() : 0,
            combinedOperations.hasTestLineOperations() ? combinedOperations.getTestLineOperations().toInsert.size() : 0,
            combinedOperations.hasTestLineOperations() ? combinedOperations.getTestLineOperations().toUpdate.size() : 0,
            combinedOperations.hasTestLineOperations() ? combinedOperations.getTestLineOperations().toDelete.size() : 0
        );
    }

    /**
     * 通用的Inspectorio数据同步方法
     * @param action API操作类型
     * @param mapper 数据库操作适配器
     * @param dataType 数据类型名称（用于日志）
     * @param param 同步参数
     */
    private <T, E> void syncInspectorioData(String action, InspectorioMapper<T, E> mapper, String dataType, InspectorioSyncParam param) {
        List<ResultData> dataList = loadData(action, param);

        int size = param.getSizeOfDb();
        int totalCount = mapper.countByExample(null);
        int page = (int) Math.ceil((double) totalCount / size);
        List<T> dataListFromDb = IntStream.range(0, page)
                .map(p -> p * size)
                .mapToObj(offset -> mapper.page(offset, size))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        
        // 从API返回的ResultData中提取所有RevisableData
        List<RevisableData> dataListFromApi = dataList.stream()
                .map(ResultData::getData)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        
        // 使用zip函数匹配API数据和数据库数据
        List<Pair<Optional<RevisableData>, Optional<T>>> pairs = zipByMatchingId(dataListFromApi, dataListFromDb, mapper);
        
        JobLogUtil.info(log, "zipByMatchingId completed for {}, total pairs: {}", dataType, pairs.size());
        
        // 计算需要新增、更新和删除的数据库记录
        GenericDatabaseOperations<T> operations = calculateDatabaseOperations(pairs, mapper, dataType, param.getDatePatterns());
        
        // 执行数据库IO操作
        executeDatabaseOperations(operations, mapper, dataType);
        
        JobLogUtil.info(log, "sync{}完成，新增: {}, 更新: {}, 删除: {}", 
            dataType, operations.toInsert.size(), operations.toUpdate.size(), operations.toDelete.size());
    }

    /**
     * 同时同步Package和TestLine数据
     */
    private CombinedSyncOperations syncInspectorioDataCombined(InspectorioSyncParam param) {
        // 同步Package数据
        List<ResultData> packageDataList = loadData("GetTestPackageList", param);
        GenericDatabaseOperations<InspectorioPackageInfoPO> packageOperations = performSyncOperations(packageDataList, packageMapperMgr, "PackageInfo", param);
        
        // 同步TestLine数据
        List<ResultData> testLineDataList = loadData("GetTestPropertyList", param);
        GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOperations = performSyncOperations(testLineDataList, testLineMapperMgr, "TestLineInfo", param);
        
        return new CombinedSyncOperations(packageOperations, testLineOperations);
    }

    /**
     * 执行同步操作的通用方法
     */
    @SuppressWarnings("unchecked")
    private <T, E> GenericDatabaseOperations<T> performSyncOperations(List<ResultData> dataList, InspectorioMapper<T, E> mapper, String dataType, InspectorioSyncParam param) {
        int size = param.getSizeOfDb();
        int totalCount = mapper.countByExample(null);
        int page = (int) Math.ceil((double) totalCount / size);
        List<T> dataListFromDb = IntStream.range(0, page)
                .map(p -> p * size)
                .mapToObj(offset -> mapper.page(offset, size))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        
        // 从API返回的ResultData中提取所有RevisableData
        List<RevisableData> dataListFromApi = dataList.stream()
                .map(ResultData::getData)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        
        // 使用zip函数匹配API数据和数据库数据
        List<Pair<Optional<RevisableData>, Optional<T>>> pairs = zipByMatchingId(dataListFromApi, dataListFromDb, mapper);
        
        JobLogUtil.info(log, "zipByMatchingId completed for {}, total pairs: {}", dataType, pairs.size());
        
        // 计算需要新增、更新和删除的数据库记录
        GenericDatabaseOperations<T> operations = calculateDatabaseOperations(pairs, mapper, dataType, param.getDatePatterns());
        
        // 执行数据库IO操作
        executeDatabaseOperations(operations, mapper, dataType);
        
        JobLogUtil.info(log, "sync{}完成，新增: {}, 更新: {}, 删除: {}", 
            dataType, operations.toInsert.size(), operations.toUpdate.size(), operations.toDelete.size());
        
        // 转换为Object类型的GenericDatabaseOperations
        return operations;
    }

    /**
     * zip操作，将API数据列表和数据库PO列表根据ID进行匹配
     * @param apiDataList API数据列表
     * @param dbDataList 数据库PO列表
     * @param mapper 数据库操作适配器
     * @return 匹配结果，API列表有而数据库列表没有的元素，Pair的右值为Optional.empty()；反之Pair的左值为Optional.empty()
     */
    public <T, E> List<Pair<Optional<RevisableData>, Optional<T>>> zipByMatchingId(List<RevisableData> apiDataList, List<T> dbDataList, InspectorioMapper<T, E> mapper) {
        // 将API数据列表转换为Map，key为getId()，value为RevisableData
        Map<String, RevisableData> apiMap = apiDataList.stream()
                .collect(Collectors.toMap(
                        RevisableData::getId, 
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的id，保留第一个
                ));
        
        // 将数据库PO列表转换为Map，key为getExternalId()，value为PO对象
        Map<String, T> dbMap = dbDataList.stream()
                .collect(Collectors.toMap(
                        mapper::getExternalId, 
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的id，保留第一个
                ));
        
        List<Pair<Optional<RevisableData>, Optional<T>>> result = new ArrayList<>();
        
        // 获取所有唯一的id
        Set<String> allIds = new HashSet<>();
        allIds.addAll(apiMap.keySet());
        allIds.addAll(dbMap.keySet());
        
        // 处理每个id
        for (String id : allIds) {
            RevisableData apiData = apiMap.get(id);
            T dbData = dbMap.get(id);
            
            if (apiData != null && dbData == null) {
                // API列表有，数据库列表没有 - Pair的右值为Optional.empty()
                result.add(Pair.of(Optional.of(apiData), Optional.empty()));
            } else if (apiData == null && dbData != null) {
                // API列表没有，数据库列表有 - Pair的左值为Optional.empty()
                result.add(Pair.of(Optional.empty(), Optional.of(dbData)));
            } else if (apiData != null && dbData != null) {
                // 两个列表都有
                result.add(Pair.of(Optional.of(apiData), Optional.of(dbData)));
            }
            // apiData == null && dbData == null 的情况不应该出现，因为id是从两个map的key中获取的
        }
        
        return result;
    }

    /**
     * 计算需要执行的数据库操作
     * @param pairs API数据和数据库数据的匹配结果
     * @param mapper 数据库操作适配器
     * @param dataType 数据类型名称（用于日志）
     * @param datePatterns 日期格式模式列表
     * @return 包含新增、更新、删除记录的操作对象
     */
    private <T, E> GenericDatabaseOperations<T> calculateDatabaseOperations(List<Pair<Optional<RevisableData>, Optional<T>>> pairs, InspectorioMapper<T, E> mapper, String dataType, List<String> datePatterns) {
        // 第一步：分析pairs，确定操作类型
        List<RevisableData> toInsertData = new ArrayList<>();
        List<String> toDeleteIds = new ArrayList<>();
        List<RevisableData> toUpdateData = new ArrayList<>();
        
        for (Pair<Optional<RevisableData>, Optional<T>> pair : pairs) {
            Optional<RevisableData> apiData = pair.getFirst();
            Optional<T> dbData = pair.getSecond();
            
            if (apiData.isPresent() && !dbData.isPresent()) {
                // 左值有，右值没有：新增
                toInsertData.add(apiData.get());
                JobLogUtil.info(log, "标记新增，externalId: {}", apiData.get().getId());
                
            } else if (!apiData.isPresent() && dbData.isPresent()) {
                // 左值没有，右值有：逻辑删除
                T dbItem = dbData.get();
                if (mapper.getActiveIndicator(dbItem) == 1) {
                    toDeleteIds.add(mapper.getExternalId(dbItem));
                    JobLogUtil.info(log, "标记删除，externalId: {}", mapper.getExternalId(dbItem));
                }
                
            } else if (apiData.isPresent() && dbData.isPresent()) {
                // 左右值都有：比较revision
                RevisableData apiItem = apiData.get();
                T dbItem = dbData.get();
                
                // 将数据库PO转换为RevisableData以便比较revision
                RevisableData dbItemData = JSONObject.parseObject(mapper.getData(dbItem), RevisableData.class);
                if (apiItem.diffRevision(dbItemData)) {
                    toUpdateData.add(apiItem);
                    JobLogUtil.info(log, "标记更新，externalId: {}, oldRevision: {}, newRevision: {}", 
                        apiItem.getId(), dbItemData.getRevision(), apiItem.getRevision());
                }
            }
        }
        
        JobLogUtil.info(log, "操作分析完成，新增: {}, 更新: {}, 删除: {}", 
            toInsertData.size(), toUpdateData.size(), toDeleteIds.size());
        
        // 第二步：从pairs中直接获取数据库记录，无需额外查询
        Map<String, T> dbRecordsMap = new HashMap<>();
        
        // 从pairs中提取数据库记录
        for (Pair<Optional<RevisableData>, Optional<T>> pair : pairs) {
            if (pair.getSecond().isPresent()) {
                T dbRecord = pair.getSecond().get();
                dbRecordsMap.put(mapper.getExternalId(dbRecord), dbRecord);
            }
        }
        
        JobLogUtil.info(log, "从pairs提取数据库记录完成，提取到 {} 条记录", dbRecordsMap.size());
        
        // 第三步：构建数据库操作对象
        List<T> toInsert = new ArrayList<>();
        List<T> toUpdate = new ArrayList<>();
        List<T> toDelete = new ArrayList<>();
        
        // 构建新增记录
        for (RevisableData insertData : toInsertData) {
            T newRecord = mapper.convertFromRevisableData(insertData, RefSystemIdEnum.LULULEMON_INSPECTORIO, datePatterns);
            toInsert.add(newRecord);
        }
        
        // 构建删除记录
        for (String deleteId : toDeleteIds) {
            T existingRecord = dbRecordsMap.get(deleteId);
            if (existingRecord != null) {
                T deleteRecord = mapper.createCopyForDelete(existingRecord);
                toDelete.add(deleteRecord);
            }
        }
        
        // 构建更新记录
        for (RevisableData updateData : toUpdateData) {
            T existingRecord = dbRecordsMap.get(updateData.getId());
            if (existingRecord != null) {
                T updateRecord = mapper.createCopyForUpdate(existingRecord, updateData, RefSystemIdEnum.LULULEMON_INSPECTORIO, datePatterns);
                toUpdate.add(updateRecord);
            }
        }
        
        return new GenericDatabaseOperations<>(toInsert, toUpdate, toDelete);
    }

    /**
     * 执行数据库IO操作
     * @param operations 包含新增、更新、删除记录的操作对象
     * @param mapper 数据库操作适配器
     * @param dataType 数据类型名称（用于日志）
     */
    private <T, E> void executeDatabaseOperations(GenericDatabaseOperations<T> operations, InspectorioMapper<T, E> mapper, String dataType) {
        // 批量执行数据库操作
        if (!operations.toInsert.isEmpty()) {
            int insertedCount = mapper.batchInsert(operations.toInsert);
            JobLogUtil.info(log, "批量新增{}完成，插入记录数: {}", dataType, insertedCount);
        }
        
        if (!operations.toUpdate.isEmpty()) {
            int updatedCount = mapper.batchUpdate(operations.toUpdate);
            JobLogUtil.info(log, "批量更新{}完成，更新记录数: {}", dataType, updatedCount);
        }
        
        if (!operations.toDelete.isEmpty()) {
            int deletedCount = mapper.batchUpdate(operations.toDelete);
            JobLogUtil.info(log, "批量逻辑删除{}完成，删除记录数: {}", dataType, deletedCount);
        }
    }

    @NotNull
    private List<ResultData> loadData(String action, InspectorioSyncParam param) {
        final int limit = param.getSizeOfApi();
        final int maxSize = param.getMaxSizeOfApi();
        List<ResultData> dataList = new ArrayList<>();
        
        // Load first page to get total count
        ResultData firstPage = loadPageData(action, 0, limit);
        dataList.add(firstPage);
        
        // Calculate total pages
        int totalRecords = Math.min(firstPage.getTotal(), maxSize);
        int totalPages = (int) Math.ceil((double) totalRecords / limit);
        JobLogUtil.info(log, "after first page load, totalPages: {}, maxSize: {}", totalPages, maxSize);
        
        // Load remaining pages
        IntStream.range(1, totalPages)
                .map(page -> page * limit)
                .filter(offset -> offset < maxSize)
                .mapToObj(offset -> loadPageData(action, offset, limit))
                .forEach(dataList::add);

        return dataList;
    }

    private ResultData loadPageData(String action, int offset, int limit) {
        CustomResult<ResultData> pageData = iLayerClient.getInspectorioLabTestInfo(action, RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId(), offset, limit);
        JobLogUtil.info(log, "loadPageData, action: {}, offset: {}, limit: {}, success: {}", action, offset, limit, pageData.isSuccess());
        if (pageData.isSuccess()) {
            return pageData.getData();
        } else {
            throw new RuntimeException(pageData.getMsg());
        }
    }

    /**
     * 发送邮件通知
     * @param combinedOperations 组合同步操作结果
     */
    private void sendCombinedSyncNotificationEmail(CombinedSyncOperations combinedOperations) {
        try {
            Optional<CustomerGeneralConfig> customerGeneralConfigOpt = Optional.ofNullable(configClient.getConfig(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId(), CONFIG_CUSTOMER_CONFIG))
                    .map(configValue -> JSON.parseObject(configValue, CustomerGeneralConfig.class));

            if (!customerGeneralConfigOpt.isPresent()) {
                JobLogUtil.info(log, "No customer config found for Inspectorio sync, skip sending email");
                return;
            }

            CustomerGeneralConfig customerGeneralConfig = customerGeneralConfigOpt.get();
            CustomerGeneralConfig.Emails emails = customerGeneralConfig.getEmails();
            if (Func.isEmpty(emails)) {
                JobLogUtil.info(log, "No emails config found for Inspectorio sync, skip sending email");
                return;
            }

            CustomerGeneralConfig.EmailNode emailNode = emails.getInspectorioSync();
            if (emailNode == null) {
                JobLogUtil.info(log, "No email config found for Inspectorio sync, skip sending email");
                return;
            }

            sendCombinedEmailNotification(emailNode, combinedOperations, customerGeneralConfig);
        } catch (Exception e) {
            log.error("Failed to send combined sync notification email", e);
        }
    }

    /**
     * 发送邮件通知
     * @param emailNode 邮件配置节点
     * @param combinedOperations 组合同步操作结果
     * @param customerGeneralConfig 客户配置
     */
    private void sendCombinedEmailNotification(CustomerGeneralConfig.EmailNode emailNode, CombinedSyncOperations combinedOperations, CustomerGeneralConfig customerGeneralConfig) {
        String mailSubject = emailNode.getMailSubject();
        String mailText = emailNode.getMailText();
        Set<String> mailCc = emailNode.getMailCc();

        // 获取操作统计，处理null情况
        GenericDatabaseOperations<InspectorioPackageInfoPO> packageOps = combinedOperations.packageOperations;
        GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOps = combinedOperations.testLineOperations;
        
        int packageInsertCount = packageOps != null ? packageOps.toInsert.size() : 0;
        int packageUpdateCount = packageOps != null ? packageOps.toUpdate.size() : 0;
        int packageDeleteCount = packageOps != null ? packageOps.toDelete.size() : 0;
        int testLineInsertCount = testLineOps != null ? testLineOps.toInsert.size() : 0;
        int testLineUpdateCount = testLineOps != null ? testLineOps.toUpdate.size() : 0;
        int testLineDeleteCount = testLineOps != null ? testLineOps.toDelete.size() : 0;

        // 构建邮件变量
        Map<String, Object> variables = new HashMap<>();
        variables.put("customerName", customerGeneralConfig.getCustomerNameEn());
        variables.put("packageInsertCount", packageInsertCount);
        variables.put("packageUpdateCount", packageUpdateCount);
        variables.put("packageDeleteCount", packageDeleteCount);
        variables.put("packageTotalCount", packageInsertCount + packageUpdateCount + packageDeleteCount);
        variables.put("testLineInsertCount", testLineInsertCount);
        variables.put("testLineUpdateCount", testLineUpdateCount);
        variables.put("testLineDeleteCount", testLineDeleteCount);
        variables.put("testLineTotalCount", testLineInsertCount + testLineUpdateCount + testLineDeleteCount);
        variables.put("totalInsertCount", packageInsertCount + testLineInsertCount);
        variables.put("totalUpdateCount", packageUpdateCount + testLineUpdateCount);
        variables.put("totalDeleteCount", packageDeleteCount + testLineDeleteCount);
        variables.put("totalCount", packageInsertCount + packageUpdateCount + packageDeleteCount + testLineInsertCount + testLineUpdateCount + testLineDeleteCount);
        variables.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 构建操作详情
        List<String> operationDetails = new ArrayList<>();
        if (packageInsertCount > 0) {
            operationDetails.add(String.format("Package新增: %d 条记录", packageInsertCount));
        }
        if (packageUpdateCount > 0) {
            operationDetails.add(String.format("Package更新: %d 条记录", packageUpdateCount));
        }
        if (packageDeleteCount > 0) {
            operationDetails.add(String.format("Package删除: %d 条记录", packageDeleteCount));
        }
        if (testLineInsertCount > 0) {
            operationDetails.add(String.format("TestLine新增: %d 条记录", testLineInsertCount));
        }
        if (testLineUpdateCount > 0) {
            operationDetails.add(String.format("TestLine更新: %d 条记录", testLineUpdateCount));
        }
        if (testLineDeleteCount > 0) {
            operationDetails.add(String.format("TestLine删除: %d 条记录", testLineDeleteCount));
        }
        variables.put("operationDetails", String.join(", ", operationDetails));

        // 构建表格内容
        String packageUpdateTable = buildPackageUpdateTable(packageOps);
        String propertyUpdateTable = buildPropertyUpdateTable(testLineOps);

        // 添加表格变量
        variables.put("packageUpdateTable", packageUpdateTable);
        variables.put("propertyUpdateTable", propertyUpdateTable);

        // 替换邮件模板变量
        String finalMailSubject = replaceVariables(mailSubject, variables);
        String finalMailText = replaceVariables(mailText, variables);

        // 获取收件人列表
        Set<String> recipients = new HashSet<>();
        if (emailNode.getDefaultMailTo() != null) {
            recipients.addAll(emailNode.getDefaultMailTo());
        }
        
        // 这里可以根据实际需求添加其他逻辑来确定收件人
        // 例如根据labCode等条件选择特定的收件人

        EMailRequest emailRequest = EMailRequest.builder()
                .mailSubject(finalMailSubject)
                .mailText(finalMailText)
                .mailTo(recipients)
                .mailCc(mailCc)
                .build();

        try {
            boolean success = emailClient.sendEmail(emailRequest);
            if (success) {
                JobLogUtil.info(log, "Successfully sent combined sync notification email");
            } else {
                log.error("Failed to send combined sync notification email");
            }
        } catch (Exception e) {
            log.error("Exception occurred while sending combined sync notification email", e);
        }
    }

    /**
     * 替换邮件模板中的变量
     * @param template 邮件模板
     * @param variables 变量映射
     * @return 替换后的内容
     */
    private String replaceVariables(String template, Map<String, Object> variables) {
        if (Func.isBlank(template)) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        return result;
    }

    /**
     * 构建Package更新列表表格
     * @param packageOps Package操作对象
     * @return 表格HTML字符串
     */
    private String buildPackageUpdateTable(GenericDatabaseOperations<InspectorioPackageInfoPO> packageOps) {
        StringBuilder table = new StringBuilder();
        table.append("<p><strong>Package update list:</strong></p>");
        table.append("<table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse;\">");
        table.append("<thead>");
        table.append("<tr style=\"background-color: #f2f2f2;\">");
        table.append("<th>Package Id</th>");
        table.append("<th>Package Name</th>");
        table.append("<th>Update</th>");
        table.append("</tr>");
        table.append("</thead>");
        table.append("<tbody>");

        boolean hasData = false;

        if (packageOps != null) {
            // 添加Updated记录
            if (packageOps.toUpdate != null) {
                for (InspectorioPackageInfoPO pkg : packageOps.toUpdate) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #ff9900;\">Updated</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }

            // 添加Delete记录
            if (packageOps.toDelete != null) {
                for (InspectorioPackageInfoPO pkg : packageOps.toDelete) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #ff0000;\">Delete</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }

            // 添加New记录
            if (packageOps.toInsert != null) {
                for (InspectorioPackageInfoPO pkg : packageOps.toInsert) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(pkg.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #00aa00;\">New</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }
        }

        // 如果没有任何记录，添加空行
        if (!hasData) {
            table.append("<tr>");
            table.append("<td colspan=\"3\" style=\"text-align: center; color: #999999;\">No updates</td>");
            table.append("</tr>");
        }

        table.append("</tbody>");
        table.append("</table>");

        return table.toString();
    }

    /**
     * 构建Property更新列表表格
     * @param testLineOps TestLine操作对象
     * @return 表格HTML字符串
     */
    private String buildPropertyUpdateTable(GenericDatabaseOperations<InspectorioTestLineInfoPO> testLineOps) {
        StringBuilder table = new StringBuilder();
        table.append("<p><strong>Property update list:</strong></p>");
        table.append("<table border=\"1\" cellpadding=\"5\" cellspacing=\"0\" style=\"border-collapse: collapse;\">");
        table.append("<thead>");
        table.append("<tr style=\"background-color: #f2f2f2;\">");
        table.append("<th>Property Id</th>");
        table.append("<th>Property Name</th>");
        table.append("<th>Update</th>");
        table.append("</tr>");
        table.append("</thead>");
        table.append("<tbody>");

        boolean hasData = false;

        if (testLineOps != null) {
            // 添加Updated记录
            if (testLineOps.toUpdate != null) {
                for (InspectorioTestLineInfoPO testLine : testLineOps.toUpdate) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #ff9900;\">Updated</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }

            // 添加Delete记录
            if (testLineOps.toDelete != null) {
                for (InspectorioTestLineInfoPO testLine : testLineOps.toDelete) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #ff0000;\">Delete</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }

            // 添加New记录
            if (testLineOps.toInsert != null) {
                for (InspectorioTestLineInfoPO testLine : testLineOps.toInsert) {
                    table.append("<tr>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalId())).append("</td>");
                    table.append("<td>").append(escapeHtml(testLine.getExternalName())).append("</td>");
                    table.append("<td style=\"color: #00aa00;\">New</td>");
                    table.append("</tr>");
                    hasData = true;
                }
            }
        }

        // 如果没有任何记录，添加空行
        if (!hasData) {
            table.append("<tr>");
            table.append("<td colspan=\"3\" style=\"text-align: center; color: #999999;\">No updates</td>");
            table.append("</tr>");
        }

        table.append("</tbody>");
        table.append("</table>");

        return table.toString();
    }

    /**
     * HTML转义方法，防止XSS攻击
     * @param text 需要转义的文本
     * @return 转义后的文本
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;");
    }

    /**
     * 同步Lululemon TRF信息
     *
     * @param param 同步参数
     */
    public void syncLululemonTrfInfo(com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam param) {
        log.info("开始同步Lululemon TRF信息，参数: {}", param);

        try {
            // 验证参数
            if (!JobParamParser.validateParam(param)) {
                throw new IllegalArgumentException("参数验证失败");
            }

            // 设置系统ID为Lululemon
            param.setRefSystemId(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());

            // 执行同步
            syncTrfInfo(param);

            log.info("Lululemon TRF信息同步完成");

        } catch (Exception e) {
            log.error("同步Lululemon TRF信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("同步Lululemon TRF信息失败", e);
        }
    }

    /**
     * 同步Target TRF信息
     *
     * @param param 同步参数
     */
    public void syncTargetTrfInfo(com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam param) {
        log.info("开始同步Target TRF信息，参数: {}", param);

        try {
            // 验证参数
            if (!JobParamParser.validateParam(param)) {
                throw new IllegalArgumentException("参数验证失败");
            }

            // 设置系统ID为Target
            param.setRefSystemId(RefSystemIdEnum.TARGET_INSPECTORIO.getRefSystemId());

            // 执行同步
            syncTrfInfo(param);

            log.info("Target TRF信息同步完成");

        } catch (Exception e) {
            log.error("同步Target TRF信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("同步Target TRF信息失败", e);
        }
    }

    /**
     * 执行TRF信息同步的核心方法
     *
     * @param param 同步参数
     */
    private void syncTrfInfo(com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam param) {
        log.info("开始执行TRF信息同步，系统ID: {}, 时间范围: {} - {}",
            param.getRefSystemId(), param.getUpdatedFrom(), param.getUpdatedTo());

        try {
            // 构建查询请求
            JSONObject requestBody = buildTrfQueryRequest(param);

            // 调用Inspectorio API获取TRF列表
            CustomResult<Object> apiResult = iLayerClient.queryCustomerTrfList(requestBody);

            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.warn("获取TRF数据失败或无数据返回: {}", apiResult);
                return;
            }

            // 解析API响应数据
            List<JSONObject> trfDataList = parseTrfApiResponse(apiResult.getData());

            if (trfDataList.isEmpty()) {
                log.info("未获取到TRF数据");
                return;
            }

            log.info("获取到 {} 条TRF数据", trfDataList.size());

            // 转换为数据库实体对象
            List<InspectorioTrfInfoPO> trfInfoList = inspectorioTrfConverter.convertToTrfInfoPOList(trfDataList, param.getRefSystemId());

            if (trfInfoList.isEmpty()) {
                log.warn("转换后无有效TRF数据");
                return;
            }

            // 执行数据库操作
            performTrfDatabaseOperations(trfInfoList, param);

            log.info("TRF信息同步完成，处理了 {} 条数据", trfInfoList.size());

        } catch (Exception e) {
            log.error("执行TRF信息同步失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行TRF信息同步失败", e);
        }
    }

    /**
     * 构建TRF查询请求
     */
    private JSONObject buildTrfQueryRequest(com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam param) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("updatedFrom", param.getUpdatedFrom());
        requestBody.put("updatedTo", param.getUpdatedTo());
        requestBody.put("size", param.getSizeOfApi());
        requestBody.put("refSystemId", param.getRefSystemId());

        return requestBody;
    }

    /**
     * 解析TRF API响应数据
     */
    @SuppressWarnings("unchecked")
    private List<JSONObject> parseTrfApiResponse(Object responseData) {
        List<JSONObject> result = new ArrayList<>();

        try {
            if (responseData instanceof List) {
                List<Object> dataList = (List<Object>) responseData;
                for (Object item : dataList) {
                    if (item instanceof JSONObject) {
                        result.add((JSONObject) item);
                    } else {
                        // 尝试转换为JSONObject
                        JSONObject jsonItem = JSONObject.parseObject(JSON.toJSONString(item));
                        result.add(jsonItem);
                    }
                }
            } else if (responseData instanceof JSONObject) {
                JSONObject jsonData = (JSONObject) responseData;
                if (jsonData.containsKey("data") && jsonData.get("data") instanceof List) {
                    return parseTrfApiResponse(jsonData.get("data"));
                } else {
                    result.add(jsonData);
                }
            }
        } catch (Exception e) {
            log.error("解析TRF API响应数据失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 执行TRF数据库操作
     */
    private void performTrfDatabaseOperations(List<InspectorioTrfInfoPO> trfInfoList, com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam param) {
        if (trfInfoList == null || trfInfoList.isEmpty()) {
            return;
        }

        int insertCount = 0;
        int updateCount = 0;

        try {
            // 分批处理数据
            int batchSize = param.getSizeOfDb();
            for (int i = 0; i < trfInfoList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, trfInfoList.size());
                List<InspectorioTrfInfoPO> batch = trfInfoList.subList(i, endIndex);

                for (InspectorioTrfInfoPO trfInfo : batch) {
                    try {
                        // 检查是否已存在
                        InspectorioTrfInfoExample example = new InspectorioTrfInfoExample();
                        example.createCriteria()
                            .andRefSystemIdEqualTo(trfInfo.getRefSystemId())
                            .andTrfNoEqualTo(trfInfo.getTrfNo());

                        List<InspectorioTrfInfoPO> existingList = inspectorioTrfInfoMapper.selectByExample(example);

                        if (existingList.isEmpty()) {
                            // 插入新记录
                            inspectorioTrfInfoMapper.insertSelective(trfInfo);
                            insertCount++;
                            log.debug("插入新TRF记录: {}", inspectorioTrfConverter.extractKeyInfo(JSON.parseObject((String) trfInfo.getRawData())));
                        } else {
                            // 更新现有记录
                            InspectorioTrfInfoPO existingTrf = existingList.get(0);
                            JSONObject newData = JSON.parseObject((String) trfInfo.getRawData());
                            inspectorioTrfConverter.updateTrfInfoPO(existingTrf, newData);

                            inspectorioTrfInfoMapper.updateByPrimaryKeySelective(existingTrf);
                            updateCount++;
                            log.debug("更新TRF记录: {}", inspectorioTrfConverter.extractKeyInfo(newData));
                        }

                    } catch (Exception e) {
                        log.error("处理TRF记录失败: {}, 错误: {}",
                            inspectorioTrfConverter.extractKeyInfo(JSON.parseObject((String) trfInfo.getRawData())),
                            e.getMessage());
                    }
                }

                log.info("处理批次 {}-{}, 插入: {}, 更新: {}", i + 1, endIndex, insertCount, updateCount);
            }

            log.info("TRF数据库操作完成，总计插入: {}, 更新: {}", insertCount, updateCount);

        } catch (Exception e) {
            log.error("执行TRF数据库操作失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行TRF数据库操作失败", e);
        }
    }
}
