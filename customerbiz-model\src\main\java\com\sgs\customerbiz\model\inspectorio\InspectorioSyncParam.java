package com.sgs.customerbiz.model.inspectorio;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * Inspectorio TRF同步参数类
 * 扩展现有的InspectorioSyncParam以支持TRF同步功能
 * 
 * <AUTHOR> System
 */
@Data
public class InspectorioSyncParam {
    
    /**
     * API每次查询的数量
     */
    private Integer sizeOfApi = 50;
    
    /**
     * 数据库每次处理的数量
     */
    private Integer sizeOfDb = 100;
    
    /**
     * API查询的最大数量限制
     */
    private Integer maxSizeOfApi = 1000;
    
    /**
     * 时间范围字符串，如 "1h", "1d", "1w"
     */
    private String timeRange;
    
    /**
     * 更新开始时间 (ISO 8601格式)
     */
    private String updatedFrom;
    
    /**
     * 更新结束时间 (ISO 8601格式)
     */
    private String updatedTo;
    
    /**
     * 系统ID (10030=Lululemon, 10028=Target)
     */
    private Integer refSystemId;
    
    /**
     * 日期格式模式列表
     */
    private List<String> datePatterns = Arrays.asList(
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.SS'Z'",
        "yyyy-MM-dd'T'HH:mm:ss.S'Z'"
    );
    
    /**
     * 构造函数
     */
    public InspectorioSyncParam() {
    }
    
    /**
     * 构造函数
     * @param sizeOfApi API查询数量
     * @param sizeOfDb 数据库处理数量
     * @param refSystemId 系统ID
     */
    public InspectorioSyncParam(Integer sizeOfApi, Integer sizeOfDb, Integer refSystemId) {
        this.sizeOfApi = sizeOfApi;
        this.sizeOfDb = sizeOfDb;
        this.refSystemId = refSystemId;
    }
    
    /**
     * 构造函数
     * @param sizeOfApi API查询数量
     * @param sizeOfDb 数据库处理数量
     * @param timeRange 时间范围
     * @param refSystemId 系统ID
     */
    public InspectorioSyncParam(Integer sizeOfApi, Integer sizeOfDb, String timeRange, Integer refSystemId) {
        this.sizeOfApi = sizeOfApi;
        this.sizeOfDb = sizeOfDb;
        this.timeRange = timeRange;
        this.refSystemId = refSystemId;
    }
}
