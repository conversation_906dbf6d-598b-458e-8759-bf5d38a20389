package com.sgs.customerbiz.web.controllers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sgs.customerbiz.biz.service.task.impl.SyncLululemonTrfInfoXXLJobScheduler;
import com.sgs.customerbiz.biz.service.task.impl.SyncTargetTrfInfoXXLJobScheduler;
import com.sgs.customerbiz.facade.ICustomerTodoListFacade;
import com.sgs.customerbiz.model.trf.dto.req.ManualSyncRequest;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TodoListController单元测试
 * 
 * <AUTHOR> System
 */
class TodoListControllerTest {

    @Mock
    private ICustomerTodoListFacade iTodoListFacade;

    @Mock
    private SyncLululemonTrfInfoXXLJobScheduler syncLululemonTrfInfoScheduler;

    @Mock
    private SyncTargetTrfInfoXXLJobScheduler syncTargetTrfInfoScheduler;

    @InjectMocks
    private TodoListController todoListController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(todoListController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testManualSync_LululemonSuccess() throws Exception {
        // 准备测试数据
        ManualSyncRequest request = new ManualSyncRequest();
        request.setRefSystemId(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
        request.setTimeRange("1h");
        request.setSizeOfApi(50);
        request.setSizeOfDb(100);

        // Mock行为
        doNothing().when(syncLululemonTrfInfoScheduler).manualSync(any());

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.refSystemId").value(request.getRefSystemId()))
                .andExpect(jsonPath("$.data.duration").exists());
    }

    @Test
    void testManualSync_TargetSuccess() throws Exception {
        // 准备测试数据
        ManualSyncRequest request = new ManualSyncRequest();
        request.setRefSystemId(RefSystemIdEnum.TARGET_INSPECTORIO.getRefSystemId());
        request.setTimeRange("2h");

        // Mock行为
        doNothing().when(syncTargetTrfInfoScheduler).manualSync(any());

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.refSystemId").value(request.getRefSystemId()));
    }

    @Test
    void testManualSync_UnsupportedSystemId() throws Exception {
        // 准备测试数据
        ManualSyncRequest request = new ManualSyncRequest();
        request.setRefSystemId(99999); // 不支持的系统ID
        request.setTimeRange("1h");

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("不支持的系统ID")));
    }

    @Test
    void testManualSync_MissingRequiredFields() throws Exception {
        // 准备测试数据 - 缺少必填字段
        ManualSyncRequest request = new ManualSyncRequest();
        // 不设置refSystemId和timeRange

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()); // 应该返回400错误
    }

    @Test
    void testManualSync_InvalidTimeRange() throws Exception {
        // 准备测试数据
        ManualSyncRequest request = new ManualSyncRequest();
        request.setRefSystemId(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
        request.setTimeRange("invalid-time-range");

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("参数验证失败")));
    }

    @Test
    void testManualSync_SchedulerException() throws Exception {
        // 准备测试数据
        ManualSyncRequest request = new ManualSyncRequest();
        request.setRefSystemId(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
        request.setTimeRange("1h");

        // Mock异常行为
        doThrow(new RuntimeException("同步失败")).when(syncLululemonTrfInfoScheduler).manualSync(any());

        // 执行测试
        mockMvc.perform(post("/todoList/manualSync")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("同步失败")));
    }
}
