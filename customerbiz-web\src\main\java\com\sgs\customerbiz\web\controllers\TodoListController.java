package com.sgs.customerbiz.web.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.customerbiz.biz.service.task.impl.SyncLululemonTrfInfoXXLJobScheduler;
import com.sgs.customerbiz.biz.service.task.impl.SyncTargetTrfInfoXXLJobScheduler;
import com.sgs.customerbiz.biz.utils.JobParamParser;
import com.sgs.customerbiz.facade.ICustomerTodoListFacade;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfTodoDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.CheckTrfInfoRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.GetTrfDetailPrintRsp;
import com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam;
import com.sgs.customerbiz.model.trf.dto.req.ManualSyncRequest;
import com.sgs.customerbiz.model.trf.dto.resp.ManualSyncResponse;
import com.sgs.framework.core.base.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.ws.rs.PathParam;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/todoList")
@Api(value = "/todoList", tags = "Todo List")
public class TodoListController {

    @Autowired
    private ICustomerTodoListFacade iTodoListFacade;

    @Autowired
    private SyncLululemonTrfInfoXXLJobScheduler syncLululemonTrfInfoScheduler;

    @Autowired
    private SyncTargetTrfInfoXXLJobScheduler syncTargetTrfInfoScheduler;

    /**
     * @param queryRefSystemId
     * @return
     */
    @PostMapping("/refSystemId")
    @ApiOperation(value = "refSystemId")
    public BaseResponse refSystemId(@RequestBody QueryRefSystemId queryRefSystemId) {
        return iTodoListFacade.refSystemId(queryRefSystemId);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/trfTodoList")
    @ApiOperation(value = "trfTodoList")
    public BaseResponse trfTodoList(@RequestBody TrfTodoReq reqObject) {
        return iTodoListFacade.trfTodoList(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/importTrfNo")
    @ApiOperation(value = "importTrfNo")
    public BaseResponse importTrfNo(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.importTrfNo(reqObject);
    }

    @Deprecated
    @PostMapping("/updateTrfInfo")
    @ApiOperation(value = "updateTrfInfo")
    public BaseResponse updateTrfInfo(@RequestBody UpdateTrfInfoReq reqObject) {
        return iTodoListFacade.updateTrfInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getTrfInfoList")
    @ApiOperation(value = "getTrfInfoList")
    public BaseResponse getTrfInfoList(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.getTrfInfoList(reqObject);
    }

    @Deprecated
    @PostMapping("/getTrfInfo")
    @ApiOperation(value = "getTrfInfo")
    public BaseResponse getTrfInfo(@RequestBody TrfInfoReq reqObject) {
        return iTodoListFacade.getTrfInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/checkTrfInfo")
    @ApiOperation(value = "checkTrfInfo")
    public BaseResponse<CheckTrfInfoRsp> checkTrfInfo(@RequestBody CheckTrfInfoReq reqObject) {
        return iTodoListFacade.checkTrfInfo(reqObject);
    }

    @Deprecated
    @PostMapping("/updateTrfBoundStatus")
    @ApiOperation(value = "updateTrfBoundStatus")
    public BaseResponse updateTrfBoundStatus(@RequestBody TrfBoundStatusReq reqObject) {
        return iTodoListFacade.updateTrfBoundStatus(reqObject);
    }

    @PostMapping("/bindTrf")
    @ApiOperation(value = "bindTrf")
    public BaseResponse bindTrf(@RequestBody BindTrfReq reqObject) {
        return iTodoListFacade.bindTrf(reqObject);
    }

    @PostMapping("/unBindTrf")
    @ApiOperation(value = "unBindTrf")
    public BaseResponse unBindTrf(@RequestBody UnBindTrfReq reqObject) {
        return iTodoListFacade.unBindTrf(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/exportTrfInfoList")
    @ApiOperation(value = "exportTrfInfoList")
    public BaseResponse exportTrfInfoList(@RequestBody JsonNode reqObject) {
        return iTodoListFacade.exportTrfInfoList(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getTrfDetailInfo")
    @ApiOperation(value = "getTrfDetailInfo")
    public BaseResponse getTrfDetailInfo(@RequestBody TrfDetailReq reqObject) {
        return iTodoListFacade.getTrfDetailInfo(reqObject);
    }

    /**
     * @return
     */
    @PostMapping("/getCustomerTrfDetailInfo")
    @ApiOperation(value = "getCustomerTrfDetailInfo")
    public BaseResponse getCustomerTrfDetailInfo(@RequestBody TrfDetailReq reqObject) throws JsonProcessingException {
        return iTodoListFacade.getCustomerTrfDetailInfo(reqObject);
    }

    @PostMapping("/getTrfStatus")
    @ApiOperation(value = "getTrfStatus")
    public BaseResponse getTrfStatus(@PathParam("type") Integer type){
        return iTodoListFacade.getTrfStatus(type);
    }

    @Deprecated
    @PostMapping("/sendTrfMainInfo")
    @ApiOperation(value = "sendTrfMainInfo")
    public BaseResponse sendTrfMainInfo(@RequestBody SendTrfMainInfoReq reqObject){
        return iTodoListFacade.sendTrfMainInfo(reqObject);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/searchTrfNo")
    @ApiOperation(value = "searchTrfNo")
    public BaseResponse<List<TrfTodoDTO>> searchTrfNo(@RequestBody SearchTrfNoReq reqObject){
        return iTodoListFacade.searchTrfNo(reqObject);
    }

    @PostMapping("/checkCustomerRule")
    @ApiOperation(value = "checkCustomerRule")
    public BaseResponse<Integer> checkCustomerRule(@RequestBody SearchTrfNoReq reqObject){
        return iTodoListFacade.checkCustomerRule(reqObject);
    }

    @PostMapping("/getTrfDetailPrintInfo")
    @ApiOperation(value = "getTrfDetailPrintInfo")
    public BaseResponse<GetTrfDetailPrintRsp> getTrfDetailPrintInfo(@Valid @RequestBody GetTrfDetailPrintReq reqObject) {
        return iTodoListFacade.getTrfDetailPrintInfo(reqObject);
    }

    /**
     * 手动同步TRF信息
     *
     * @param request 手动同步请求
     * @return 同步结果
     */
    @PostMapping("/manualSync")
    @ApiOperation(value = "手动同步TRF信息", notes = "手动触发Inspectorio TRF信息同步")
    public BaseResponse<ManualSyncResponse> manualSync(@Valid @RequestBody ManualSyncRequest request) {
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            String startTimeStr = java.time.Instant.ofEpochMilli(startTime).toString();

            // 构建同步参数
            InspectorioSyncParam param = new InspectorioSyncParam();
            param.setTimeRange(request.getTimeRange());
            param.setRefSystemId(request.getRefSystemId());

            // 设置可选参数
            if (request.getSizeOfApi() != null) {
                param.setSizeOfApi(request.getSizeOfApi());
            }
            if (request.getSizeOfDb() != null) {
                param.setSizeOfDb(request.getSizeOfDb());
            }
            if (request.getMaxSizeOfApi() != null) {
                param.setMaxSizeOfApi(request.getMaxSizeOfApi());
            }

            // 解析时间范围
            JobParamParser.parseTimeRange(param);

            // 验证参数
            if (!JobParamParser.validateParam(param)) {
                return BaseResponse.<ManualSyncResponse>newFailInstance("参数验证失败");
            }

            // 根据系统ID选择对应的调度器执行同步
            if (request.getRefSystemId().equals(com.sgs.framework.model.enums.RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId())) {
                syncLululemonTrfInfoScheduler.manualSync(param);
            } else if (request.getRefSystemId().equals(com.sgs.framework.model.enums.RefSystemIdEnum.TARGET_INSPECTORIO.getRefSystemId())) {
                syncTargetTrfInfoScheduler.manualSync(param);
            } else {
                return BaseResponse.<ManualSyncResponse>newFailInstance("不支持的系统ID: " + request.getRefSystemId());
            }

            // 记录结束时间
            long endTime = System.currentTimeMillis();
            String endTimeStr = java.time.Instant.ofEpochMilli(endTime).toString();
            long duration = endTime - startTime;

            // 构建响应
            ManualSyncResponse response = new ManualSyncResponse();
            response.setStartTime(startTimeStr);
            response.setEndTime(endTimeStr);
            response.setDuration(duration);
            response.setRefSystemId(request.getRefSystemId());
            // 注意：实际的同步统计数据需要从同步服务中获取，这里暂时设置为0
            response.setSyncCount(0);
            response.setUpdateCount(0);
            response.setInsertCount(0);

            return BaseResponse.newSuccessInstance(response);

        } catch (Exception e) {
            return BaseResponse.newFailInstance("同步失败: " + e.getMessage());
        }
    }

}
