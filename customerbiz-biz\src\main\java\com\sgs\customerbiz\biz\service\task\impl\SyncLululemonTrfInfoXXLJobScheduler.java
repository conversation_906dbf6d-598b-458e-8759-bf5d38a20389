package com.sgs.customerbiz.biz.service.task.impl;

import com.sgs.customerbiz.biz.service.inspectorio.InspectorioDataBizService;
import com.sgs.customerbiz.biz.utils.JobLogUtil;
import com.sgs.customerbiz.biz.utils.JobParamParser;
import com.sgs.customerbiz.model.inspectorio.InspectorioSyncParam;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Lululemon TRF信息同步XXL-Job调度器
 * 
 * <AUTHOR> System
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SyncLululemonTrfInfoXXLJobScheduler {

    private final InspectorioDataBizService inspectorioDataBizService;

    /**
     * 同步Lululemon TRF信息的定时任务处理器
     */
    @XxlJob("syncLululemonTrfInfoHandler")
    public void syncLululemonTrfInfoHandler() {
        JobLogUtil.info(log, "开始执行Lululemon TRF信息同步任务，时间: {}", JobLogUtil.now());

        try {
            // 获取任务参数
            String jobParam = XxlJobHelper.getJobParam();
            JobLogUtil.info(log, "任务参数: {}", jobParam);

            // 解析参数
            InspectorioSyncParam param = JobParamParser.parseJobParam(jobParam, RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
            JobLogUtil.info(log, "解析后的参数: {}", param);

            // 验证参数
            if (!JobParamParser.validateParam(param)) {
                String errorMsg = "参数验证失败";
                JobLogUtil.error(log, errorMsg);
                XxlJobHelper.handleFail(errorMsg);
                return;
            }

            // 记录同步开始时间
            long startTime = System.currentTimeMillis();
            JobLogUtil.info(log, "开始同步Lululemon TRF信息，时间范围: {} - {}", 
                param.getUpdatedFrom(), param.getUpdatedTo());

            // 执行同步
            inspectorioDataBizService.syncLululemonTrfInfo(param);

            // 记录同步完成时间和耗时
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            JobLogUtil.info(log, "Lululemon TRF信息同步完成，耗时: {} ms", duration);

            // 设置任务成功
            XxlJobHelper.handleSuccess("同步完成，耗时: " + duration + " ms");

        } catch (Exception e) {
            String errorMsg = "同步Lululemon TRF信息失败: " + e.getMessage();
            JobLogUtil.error(log, e, errorMsg);
            XxlJobHelper.handleFail(errorMsg);
        }
    }

    /**
     * 手动触发同步任务（用于测试）
     * 
     * @param param 同步参数
     */
    public void manualSync(InspectorioSyncParam param) {
        log.info("手动触发Lululemon TRF信息同步，参数: {}", param);
        
        try {
            // 验证参数
            if (!JobParamParser.validateParam(param)) {
                throw new IllegalArgumentException("参数验证失败");
            }
            
            // 设置系统ID
            param.setRefSystemId(RefSystemIdEnum.LULULEMON_INSPECTORIO.getRefSystemId());
            
            // 执行同步
            inspectorioDataBizService.syncLululemonTrfInfo(param);
            
            log.info("手动同步Lululemon TRF信息完成");
            
        } catch (Exception e) {
            log.error("手动同步Lululemon TRF信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("手动同步失败", e);
        }
    }
}
